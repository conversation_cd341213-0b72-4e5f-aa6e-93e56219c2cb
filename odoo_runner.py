#!/usr/bin/env python3
"""
Odoo Development Runner
Interactive script to manage Odoo development server with commands:
- r: restart Odoo
- e: exit runner
- k: kill Odoo process
- s: start Odoo
- d: start/stop Docker PostgreSQL
- l: show logs
- h: help

Command-line usage:
  python odoo_runner.py [--start|--auto-start] [--no-interactive]

Options:
  --start, --auto-start    Start Odoo server immediately
  --no-interactive         Start server and exit (no interactive mode)
"""

import os
import sys
import subprocess
import signal
import time
import threading
import argparse
import psutil
from pathlib import Path

class OdooRunner:
    def __init__(self, auto_start=False, interactive=True):
        self.odoo_process = None
        self.docker_running = False
        self.base_dir = Path(__file__).parent
        self.venv_python = self.base_dir / "venv" / "bin" / "python"
        self.odoo_bin = self.base_dir / "odoo-bin"
        self.config_file = self.base_dir / "odoo.conf"
        self.auto_start = auto_start
        self.interactive = interactive
        self.port = 8069  # Default Odoo port

    def find_processes_on_port(self, port):
        """Find all processes using the specified port"""
        processes = []
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    for conn in proc.connections(kind='inet'):
                        if conn.laddr.port == port:
                            processes.append(proc)
                            break
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    pass
        except Exception as e:
            print(f"⚠️  Error finding processes on port {port}: {e}")
        return processes

    def kill_processes_on_port(self, port):
        """Kill all processes using the specified port"""
        processes = self.find_processes_on_port(port)
        if not processes:
            return True

        print(f"🔍 Found {len(processes)} process(es) on port {port}")
        for proc in processes:
            try:
                print(f"🔪 Killing process {proc.pid} ({proc.info['name']})")
                proc.terminate()
                # Wait up to 5 seconds for graceful termination
                proc.wait(timeout=5)
            except psutil.TimeoutExpired:
                print(f"⚡ Force killing process {proc.pid}")
                proc.kill()
            except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
                print(f"⚠️  Could not kill process {proc.pid}: {e}")

        # Verify all processes are gone
        remaining = self.find_processes_on_port(port)
        if remaining:
            print(f"⚠️  {len(remaining)} process(es) still running on port {port}")
            return False
        else:
            print(f"✅ Port {port} is now free")
            return True

    def check_docker_status(self):
        """Check if Docker PostgreSQL is running"""
        try:
            result = subprocess.run(
                ["docker", "compose", "ps", "-q", "postgres"],
                capture_output=True, text=True, cwd=self.base_dir
            )
            self.docker_running = bool(result.stdout.strip())
            return self.docker_running
        except Exception:
            return False
    
    def start_docker(self):
        """Start Docker PostgreSQL"""
        print("🐳 Starting PostgreSQL container...")
        try:
            subprocess.run(
                ["docker", "compose", "up", "-d", "postgres"],
                cwd=self.base_dir, check=True
            )
            print("✅ PostgreSQL container started")
            self.docker_running = True
            
            # Wait for PostgreSQL to be ready
            print("⏳ Waiting for PostgreSQL to be ready...")
            for i in range(30):
                result = subprocess.run(
                    ["docker", "compose", "exec", "postgres", "pg_isready", "-U", "odoo"],
                    capture_output=True, cwd=self.base_dir
                )
                if result.returncode == 0:
                    print("✅ PostgreSQL is ready")
                    return True
                time.sleep(1)
            print("❌ PostgreSQL failed to start within 30 seconds")
            return False
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to start PostgreSQL: {e}")
            return False
    
    def stop_docker(self):
        """Stop Docker PostgreSQL"""
        print("🐳 Stopping PostgreSQL container...")
        try:
            subprocess.run(
                ["docker", "compose", "down"],
                cwd=self.base_dir, check=True
            )
            print("✅ PostgreSQL container stopped")
            self.docker_running = False
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to stop PostgreSQL: {e}")
    
    def start_odoo(self):
        """Start Odoo server without database initialization"""
        if self.odoo_process and self.odoo_process.poll() is None:
            print("⚠️  Odoo is already running")
            return

        # Clean up any processes on the port before starting
        print(f"🧹 Checking for processes on port {self.port}...")
        if not self.kill_processes_on_port(self.port):
            print(f"❌ Failed to free port {self.port}. Cannot start Odoo.")
            return

        if not self.check_docker_status():
            print("🐳 PostgreSQL not running, starting it...")
            if not self.start_docker():
                return

        print("🚀 Starting Odoo server...")
        try:
            cmd = [
                str(self.venv_python),
                str(self.odoo_bin),
                "-c", str(self.config_file),
                "--dev=reload,qweb,xml"  # Removed werkzeug as WSGI is no longer used
            ]

            self.odoo_process = subprocess.Popen(
                cmd,
                cwd=self.base_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )

            # Start log monitoring in a separate thread
            log_thread = threading.Thread(target=self._monitor_logs, daemon=True)
            log_thread.start()

            print("✅ Odoo server started")
            print("🌐 Access Odoo at: http://localhost:8069")
            print("�️  Database Manager: http://localhost:8069/web/database/manager")
            print("�💡 Note: Server started without database initialization")

        except Exception as e:
            print(f"❌ Failed to start Odoo: {e}")
            # Clean up any partial processes
            if self.odoo_process:
                try:
                    self.odoo_process.terminate()
                    self.odoo_process.wait(timeout=5)
                except:
                    pass
                self.odoo_process = None
    
    def _monitor_logs(self):
        """Monitor Odoo logs in background"""
        if not self.odoo_process:
            return
        
        for line in iter(self.odoo_process.stdout.readline, ''):
            if line:
                print(f"[ODOO] {line.rstrip()}")
            if self.odoo_process.poll() is not None:
                break
    
    def kill_odoo(self):
        """Kill Odoo process"""
        if self.odoo_process and self.odoo_process.poll() is None:
            print("🔪 Killing Odoo process...")
            self.odoo_process.terminate()
            try:
                self.odoo_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.odoo_process.kill()
                self.odoo_process.wait()
            print("✅ Odoo process killed")
        else:
            print("⚠️  No Odoo process running")
    
    def restart_odoo(self):
        """Restart Odoo server"""
        print("🔄 Restarting Odoo...")
        self.kill_odoo()
        time.sleep(2)
        self.start_odoo()
    
    def show_status(self):
        """Show current status"""
        docker_status = "🟢 Running" if self.check_docker_status() else "🔴 Stopped"
        odoo_status = "🟢 Running" if (self.odoo_process and self.odoo_process.poll() is None) else "🔴 Stopped"
        
        print(f"""
📊 Status:
   PostgreSQL: {docker_status}
   Odoo:       {odoo_status}
   Config:     {self.config_file}
   URL:        http://localhost:8069
        """)
    
    def show_help(self):
        """Show help message"""
        print("""
🚀 Odoo Development Runner Commands:
   r  - Restart Odoo server
   s  - Start Odoo server
   k  - Kill Odoo process
   d  - Toggle Docker PostgreSQL (start/stop)
   l  - Show current status
   h  - Show this help
   e  - Exit runner
   
🌐 Access Odoo at: http://localhost:8069
📧 Default admin: admin / admin
        """)
    
    def run(self):
        """Main runner method - handles both interactive and non-interactive modes"""
        print("🚀 Odoo Development Runner")

        # Auto-start if requested
        if self.auto_start:
            print("🚀 Auto-starting Odoo server...")
            self.start_odoo()

            # If non-interactive, wait for server to be ready and exit
            if not self.interactive:
                print("⏳ Waiting for server to be ready...")
                self._wait_for_server_ready()
                print("✅ Server is ready! Exiting non-interactive mode.")
                return

        # Interactive mode
        if self.interactive:
            print("Type 'h' for help, 'e' to exit")

            # Show initial status
            self.show_status()

            try:
                while True:
                    try:
                        command = input("\n> ").strip().lower()

                        if command == 'e':
                            print("👋 Exiting...")
                            self.kill_odoo()
                            break
                        elif command == 'r':
                            self.restart_odoo()
                        elif command == 's':
                            self.start_odoo()
                        elif command == 'k':
                            self.kill_odoo()
                        elif command == 'd':
                            if self.check_docker_status():
                                self.stop_docker()
                            else:
                                self.start_docker()
                        elif command == 'l':
                            self.show_status()
                        elif command == 'h':
                            self.show_help()
                        elif command == '':
                            continue
                        else:
                            print(f"❓ Unknown command: {command}. Type 'h' for help.")

                    except KeyboardInterrupt:
                        print("\n👋 Exiting...")
                        self.kill_odoo()
                        break
                    except EOFError:
                        print("\n👋 Exiting...")
                        self.kill_odoo()
                        break

            finally:
                # Cleanup
                if self.odoo_process and self.odoo_process.poll() is None:
                    self.kill_odoo()

    def _wait_for_server_ready(self, timeout=60):
        """Wait for Odoo server to be ready by checking HTTP endpoint"""
        import urllib.request
        import urllib.error

        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                # Try to connect to the server
                response = urllib.request.urlopen('http://localhost:8069', timeout=5)
                if response.getcode() == 200:
                    return True
            except (urllib.error.URLError, urllib.error.HTTPError, OSError):
                pass
            time.sleep(2)

        print(f"⚠️  Server did not become ready within {timeout} seconds")
        return False

def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description="Odoo Development Runner - Interactive server management",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python odoo_runner.py                    # Interactive mode
  python odoo_runner.py --start            # Start server and enter interactive mode
  python odoo_runner.py --start --no-interactive  # Start server and exit
  python odoo_runner.py --auto-start       # Same as --start
        """
    )

    parser.add_argument(
        '--start', '--auto-start',
        action='store_true',
        dest='auto_start',
        help='Start Odoo server immediately'
    )

    parser.add_argument(
        '--no-interactive',
        action='store_true',
        help='Start server and exit (no interactive mode)'
    )

    return parser.parse_args()


if __name__ == "__main__":
    args = parse_args()

    # Determine interactive mode
    interactive = not args.no_interactive

    runner = OdooRunner(auto_start=args.auto_start, interactive=interactive)
    runner.run()
